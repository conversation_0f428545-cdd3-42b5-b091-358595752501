import { 
  containsProfanity, 
  cleanProfanity, 
  validateReviewForProfanity,
  autoCleanReviewData 
} from './profanityFilter';

// Test the profanity filter functionality
console.log('Testing Profanity Filter...');

// Test 1: Check if profanity is detected
const testText1 = 'This is a damn good service';
const testText2 = 'This is a great service';

console.log('Test 1 - Profanity Detection:');
console.log(`"${testText1}" contains profanity:`, containsProfanity(testText1));
console.log(`"${testText2}" contains profanity:`, containsProfanity(testText2));

// Test 2: Clean profanity
console.log('\nTest 2 - Profanity Cleaning:');
console.log(`Original: "${testText1}"`);
console.log(`Cleaned: "${cleanProfanity(testText1)}"`);

// Test 3: Validate review data
console.log('\nTest 3 - Review Validation:');
const reviewData = {
  title: 'Damn good service',
  review: 'This was a fucking amazing experience',
  comment: 'Great work'
};

const validation = validateReviewForProfanity(reviewData);
console.log('Validation result:', validation);

// Test 4: Auto-clean review data
console.log('\nTest 4 - Auto-clean Review Data:');
const cleanedData = autoCleanReviewData(reviewData);
console.log('Cleaned data:', cleanedData);

console.log('\nProfanity Filter Tests Complete!');

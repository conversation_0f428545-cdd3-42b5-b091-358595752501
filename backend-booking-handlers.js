// Improved Backend Booking Handlers
// Copy these handlers to your backend Express.js controller

const updateBookingStatus = async (req, res) => {
    const { bookingId } = req.params;
    const { newStatus, note } = req.body;

    try {
        const booking = await BookingService.getBookingById(bookingId);

        if (!booking) {
            return notFound(res, 'bookingId', 'Booking not found');
        }

        const { role, userId: currentUserId } = req.userData?.user || {};
        const isAdmin = role === 'admin';
        const isCustomer = booking.customerId === currentUserId;
        const isProvider = booking.providerId === currentUserId;
        const performedBy = currentUserId;

        if (!isAdmin && !isCustomer && !isProvider) {
            return forbidden(res, 'permissions', 'Access denied to update this booking status');
        }

        // Validate status
        const validStatuses = ['Pending', 'Confirmed', 'Inprogress', 'Rescheduled', 'Finished', 'Completed', 'Cancelled'];
        if (!newStatus || !validStatuses.includes(newStatus)) {
            return badRequest(res, 'newStatus', `Invalid status. Must be one of: ${validStatuses.join(', ')}`);
        }

        // Optional: Prevent invalid status transitions
        const invalidTransitions = {
            'Finished': ['Pending', 'Confirmed', 'Inprogress'],
            'Completed': ['Pending', 'Confirmed', 'Inprogress'],
            'Cancelled': ['Finished', 'Completed']
        };

        if (invalidTransitions[booking.status]?.includes(newStatus)) {
            return badRequest(res, 'newStatus', `Cannot change status from ${booking.status} to ${newStatus}`);
        }

        const updatedBooking = await BookingService.updateBookingStatus(
            bookingId,
            newStatus,
            note,
            performedBy
        );

        logger.info(
            `Booking status for Ref ${bookingId} updated to ${newStatus} by ${performedBy}`
        );

        return res.status(200).json({
            success: true,
            message: 'Booking status updated successfully',
            booking: updatedBooking,
        });
    } catch (error) {
        return handleError(res, error, bookingId, 'updating booking status');
    }
};

const rescheduleBooking = async (req, res) => {
    const { bookingId } = req.params;
    const { staffId, newDate, newFrom, newTo, note } = req.body;

    try {
        const booking = await BookingService.getBookingById(bookingId);

        if (!booking) {
            return notFound(res, 'bookingId', 'Booking not found');
        }

        const { role, userId: currentUserId } = req.userData?.user || {};
        const isAdmin = role === 'admin';
        const isCustomer = booking.customerId === currentUserId;
        const isProvider = booking.providerId === currentUserId;
        const performedBy = currentUserId;

        if (!isAdmin && !isCustomer && !isProvider) {
            return forbidden(res, 'permissions', 'Access denied to reschedule this booking');
        }

        // Business rule: prevent rescheduling of certain statuses
        const nonReschedulableStatuses = ['Cancelled', 'Completed', 'Finished'];
        if (nonReschedulableStatuses.includes(booking.status)) {
            return badRequest(res, 'bookingId', `Cannot reschedule a ${booking.status} booking`);
        }

        // Validate required fields
        if (!newDate || !newFrom || !newTo) {
            return badRequest(res, 'rescheduleData', 'Missing required fields: newDate, newFrom, newTo');
        }

        // Normalize and validate date/time
        const normalizedDate = normalizeDate(newDate);
        if (!normalizedDate) {
            return badRequest(res, 'newDate', 'Invalid date format. Use YYYY-MM-DD');
        }

        const normalizedFrom = normalizeTime(newFrom);
        const normalizedTo = normalizeTime(newTo);
        
        if (!normalizedFrom || !normalizedTo) {
            return badRequest(res, 'rescheduleData', 'Invalid time format. Use HH:MM or HH:MM AM/PM');
        }

        // Validate time logic
        const startDateTime = parseDateTime(normalizedDate, normalizedFrom);
        const endDateTime = parseDateTime(normalizedDate, normalizedTo);
        
        if (!startDateTime || !endDateTime) {
            return badRequest(res, 'rescheduleData', 'Invalid date/time combination');
        }

        if (endDateTime <= startDateTime) {
            return badRequest(res, 'rescheduleData', 'End time must be after start time');
        }

        if (startDateTime <= new Date()) {
            return badRequest(res, 'rescheduleData', 'New appointment time must be in the future');
        }

        // Prevent no-op reschedule
        const sameDate = booking.appointmentDate === normalizedDate || booking.date === normalizedDate;
        const sameFrom = normalizeTime(booking.appointmentTimeFrom || booking.from) === normalizedFrom;
        const sameTo = normalizeTime(booking.appointmentTimeTo || booking.to) === normalizedTo;
        const sameStaff = staffId ? staffId === booking.staffId : true;

        if (sameDate && sameFrom && sameTo && sameStaff) {
            return badRequest(res, 'rescheduleData', 'New schedule is identical to current schedule');
        }

        // Optional: Check for conflicts (implement if your BookingService supports it)
        if (typeof BookingService.hasConflicts === 'function') {
            const hasConflict = await BookingService.hasConflicts({
                providerId: booking.providerId,
                staffId: staffId || booking.staffId,
                date: normalizedDate,
                from: normalizedFrom,
                to: normalizedTo,
                excludeBookingId: bookingId,
            });

            if (hasConflict) {
                return conflictError(res, 'The selected time slot is not available');
            }
        }

        const updatedBooking = await BookingService.rescheduleBooking(
            bookingId,
            staffId,
            normalizedDate,
            normalizedFrom,
            normalizedTo,
            note,
            performedBy
        );

        logger.info(
            `Booking Ref ${bookingId} rescheduled to ${normalizedDate} ${normalizedFrom}-${normalizedTo} by ${performedBy}`
        );

        return res.status(200).json({
            success: true,
            message: 'Booking rescheduled successfully',
            booking: updatedBooking,
        });
    } catch (error) {
        return handleError(res, error, bookingId, 'rescheduling booking');
    }
};

// Helper functions
const notFound = (res, field, message) => {
    const errorId = errorUtil.generateErrorId();
    logger.warn(message, { errorId });
    return res.status(404).json(
        errorUtil.createErrorResponse(
            [{ field, message }],
            errorUtil.ERROR_TYPES.NOT_FOUND_ERROR,
            404,
            errorId
        )
    );
};

const forbidden = (res, field, message) => {
    const errorId = errorUtil.generateErrorId();
    logger.warn(message, { errorId });
    return res.status(403).json(
        errorUtil.createErrorResponse(
            [{ field, message }],
            errorUtil.ERROR_TYPES.FORBIDDEN_ERROR,
            403,
            errorId
        )
    );
};

const badRequest = (res, field, message) => {
    const errorId = errorUtil.generateErrorId();
    return res.status(400).json(
        errorUtil.createErrorResponse(
            [{ field, message }],
            errorUtil.ERROR_TYPES.VALIDATION_ERROR,
            400,
            errorId
        )
    );
};

const conflictError = (res, message) => {
    const errorId = errorUtil.generateErrorId();
    return res.status(409).json(
        errorUtil.createErrorResponse(
            [{ field: 'timeSlot', message }],
            errorUtil.ERROR_TYPES.CONFLICT_ERROR,
            409,
            errorId
        )
    );
};

const handleError = (res, error, bookingId, operation) => {
    const errorId = errorUtil.generateErrorId();
    logger.error(`Error ${operation} for Ref ${bookingId}: ${error.message}`, { errorId });

    const status = error.statusCode || (error.code === 'CONFLICT' ? 409 : 400);
    const type = status === 409 ? errorUtil.ERROR_TYPES.CONFLICT_ERROR : errorUtil.ERROR_TYPES.VALIDATION_ERROR;

    return res.status(status).json(
        errorUtil.createErrorResponse(
            [{ field: 'bookingId', message: error.message }],
            type,
            status,
            errorId
        )
    );
};

// Utility functions for date/time handling
const normalizeDate = (dateStr) => {
    try {
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) return null;
        return date.toISOString().slice(0, 10); // YYYY-MM-DD
    } catch {
        return null;
    }
};

const normalizeTime = (timeStr) => {
    if (!timeStr) return null;
    return to24Hour(timeStr.trim());
};

const to24Hour = (timeStr) => {
    const cleanTime = timeStr.replace(/[^0-9:apmAPM\s]/g, '');
    const ampmMatch = cleanTime.match(/([ap]m)/i);
    const ampm = ampmMatch ? ampmMatch[1].toLowerCase() : null;
    
    const timeMatch = cleanTime.match(/(\d{1,2}):?(\d{2})?/);
    if (!timeMatch) return null;
    
    let hours = parseInt(timeMatch[1], 10);
    const minutes = parseInt(timeMatch[2] || '0', 10);
    
    if (ampm === 'pm' && hours < 12) hours += 12;
    if (ampm === 'am' && hours === 12) hours = 0;
    
    if (hours > 23 || minutes > 59) return null;
    
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`;
};

const parseDateTime = (dateStr, timeStr) => {
    try {
        const [hours, minutes] = timeStr.split(':').map(Number);
        const date = new Date(`${dateStr}T00:00:00Z`);
        date.setUTCHours(hours, minutes, 0, 0);
        return date;
    } catch {
        return null;
    }
};

module.exports = {
    updateBookingStatus,
    rescheduleBooking
};

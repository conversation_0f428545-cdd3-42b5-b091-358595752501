import { FaS<PERSON><PERSON>Cart, <PERSON>a<PERSON><PERSON>, FaUser } from 'react-icons/fa';

import CustomButton from '../../../components/CustomButton';
import { Chip } from '@heroui/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useAuth } from 'react-oidc-context';

import { useNavigate } from 'react-router-dom';

import { toast } from 'react-toastify';
import { getUserBookings, cancelBooking, Booking as BookingType } from '../../../../service/bookingService';
import { ResponsiveContainer, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, PieChart, Pie, Cell, Legend } from 'recharts';
import { getImageUrlWithFallback } from '../aws/s3FileUpload';


export default function Dashboard() {
  const navigate = useNavigate();
  const [cancellingId, setCancellingId] = useState<string | number | null>(null);

  const handleViewAllBookings = useCallback(() => {
    navigate('/customer/customer-booking');
  }, [navigate]);

  // Open booking details in Booking List page
  const handleOpenBookingDetails = useCallback((booking: BookingType) => {
    const openBookingId = String(booking.bookingId || booking.id || '');
    if (!openBookingId) {
      toast.error('Unable to open booking: missing ID');
      return;
    }
    navigate('/customer/customer-booking', { state: { openBookingId } });
  }, [navigate]);


  // Booking data (from API)
  const auth = useAuth();
  const [bookingItems, setBookingItems] = useState<BookingType[]>([]);
  const [bookingsLoading, setBookingsLoading] = useState(false);

  // Get user ID from auth context
  const getUserId = useCallback(() => {
    if (auth.user && auth.user.profile) {
      const profile = auth.user.profile as {
        preferred_username?: string;
        sub?: string;
        email?: string;
      };
      return profile.preferred_username || profile.sub || profile.email || null;
    }
    return null;
  }, [auth.user]);

  // Fetch recent bookings
  const fetchBookings = useCallback(async () => {
    const uid = getUserId();
    if (!uid || !auth.isAuthenticated) return;

    try {
      setBookingsLoading(true);
      const response = await getUserBookings({ userId: uid, page: 1, limit: 6 });
      if (response && response.bookings) {
        setBookingItems(response.bookings);
      } else {
        setBookingItems([]);
      }
    } catch (error) {
      console.error('Failed to load bookings on dashboard', error);
      setBookingItems([]);
    } finally {
      setBookingsLoading(false);
    }
  }, [auth.isAuthenticated, getUserId]);

  useEffect(() => {
    if (auth.isAuthenticated && !auth.isLoading) {
      fetchBookings();
    }
  }, [auth.isAuthenticated, auth.isLoading, fetchBookings]);


  // Cancel a booking and refresh
  const handleCancelBooking = useCallback(async (id: string | number) => {
    try {
      setCancellingId(id);
      await cancelBooking(String(id));
      toast.success('Booking cancelled');
      await fetchBookings();
    } catch {
      toast.error('Failed to cancel booking');
    } finally {
      setCancellingId(null);
    }
  }, [fetchBookings]);

  // Get color classes for status
  type ChipColor = 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  const getStatusColor = (status: string): ChipColor => {
    switch (status) {
      case 'Completed':
      case 'Finished':
        return 'success';
      case 'Pending':
      case 'Inprogress':
      case 'Confirmed':
        return 'primary';
      case 'Rescheduled':
        return 'warning';
      case 'Cancelled':
        return 'danger';
      default:
        return 'default';
    }
  };

  // StatusChip component for consistent status display
  const StatusChip = ({ status }: { status: string }) => (
    <Chip
      color={getStatusColor(status)}
      variant="flat"
      size="sm"
      radius="sm"
      className="font-medium"
    >
      {status}
    </Chip>
  );

  // Metrics derived from real bookings
  const metrics = useMemo(() => {
    const totalOrders = bookingItems.length;
    const totalCompleted = bookingItems.filter((b) => b.status === 'Completed' || b.status === 'Finished').length;
    const totalCancelled = bookingItems.filter((b) => b.status === 'Cancelled').length;
    const totalSpendNumber = bookingItems.reduce((sum, b) => {
      const n = parseFloat(String(b.amount || '').replace(/[^0-9.]/g, ''));
      return sum + (isNaN(n) ? 0 : n);
    }, 0);
    const firstAmount = bookingItems.find((b) => b.amount)?.amount || '';
    const currencyMatch = String(firstAmount).match(/[^0-9\s.,-]/);
    const currencySymbol = currencyMatch ? currencyMatch[0] : '$';
    const totalSpendFormatted = `${currencySymbol}${totalSpendNumber.toFixed(2)}`;
    return { totalOrders, totalCompleted, totalCancelled, totalSpendFormatted };
  }, [bookingItems]);

  // Chart data: status distribution
  const statusData = useMemo(() => {
    const buckets: Record<string, number> = {
      Completed: 0,
      Pending: 0,
      Rescheduled: 0,
      Cancelled: 0,
    };
    bookingItems.forEach((b) => {
      const raw = String(b.status || 'Pending');
      if (raw === 'Completed' || raw === 'Finished') buckets.Completed++;
      else if (raw === 'Cancelled') buckets.Cancelled++;
      else if (raw === 'Rescheduled') buckets.Rescheduled++;
      else buckets.Pending++;
    });
    return Object.entries(buckets)
      .filter(([, v]) => v > 0)
      .map(([name, value]) => ({ name, value }));
  }, [bookingItems]);

  const STATUS_COLORS: Record<string, string> = {
    Completed: '#22c55e',
    Pending: '#3b82f6',
    Rescheduled: '#f59e0b',
    Cancelled: '#ef4444',
  };

  // Chart data: bookings over time (recent items)
  const timeSeriesData = useMemo(() => {
    type Row = { label: string; ts: number; count: number };
    const map = new Map<string, Row>();

    bookingItems.forEach((b) => {
      const d = b.date || b.appointmentDate;
      if (!d) return;
      const dt = new Date(d);
      const ts = dt.getTime();
      if (isNaN(ts)) return;
      const label = dt.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
      const row = map.get(label) || { label, ts, count: 0 };
      row.count += 1;
      map.set(label, row);
    });

    return Array.from(map.values())
      .sort((a, b) => a.ts - b.ts)
      .map(({ label, count }) => ({ date: label, count }));
  }, [bookingItems]);

  return (
    <div className="max-w-screen-xl mx-auto p-3 sm:p-4 md:p-6 w-full">
      {/* Breadcrumb & Title */}
      {/* <BreadCrumb title="Dashboard" item1="Customer" /> */}
      {/* <h2 className="text-2xl font-semibold text-gray-800 mt-4 mb-6">Dashboard</h2> */}

      {/* Total Orders */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-8">



        {/* Real totals based on bookings */}
        {[
          { title: 'Total Orders', value: metrics.totalOrders },
          { title: 'Total Spend', value: metrics.totalSpendFormatted },
          { title: 'Completed', value: metrics.totalCompleted },
          { title: 'Cancelled', value: metrics.totalCancelled },
        ].map((stat, index) => (
          <div
            key={index}
            className="bg-white p-4 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg flex items-center justify-between"
          >
            <div className="p-3 rounded-full bg-gray-100 text-lg flex items-center justify-center text-gray-600">
              <FaShoppingCart />
            </div>
            <div className="flex-1 ml-4">
              <p className="text-gray-500 text-sm font-medium">{stat.title}</p>
              <p className="text-xl font-semibold">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Bookings Over Time */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-lg text-gray-800">Bookings Over Time</h3>
          </div>
          <div className="h-64">
            {timeSeriesData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={timeSeriesData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <defs>
                    <linearGradient id="colorBookings" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#f97316" stopOpacity={0.4} />
                      <stop offset="95%" stopColor="#f97316" stopOpacity={0.05} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                  <YAxis allowDecimals={false} tick={{ fontSize: 12 }} />
                  <Tooltip />
                  <Area type="monotone" dataKey="count" stroke="#f97316" strokeWidth={2} fillOpacity={1} fill="url(#colorBookings)" isAnimationActive={true} />
                </AreaChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">No time series data yet</div>
            )}
          </div>
        </div>
        {/* Status Breakdown */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-bold text-lg text-gray-800">Status Breakdown</h3>
          </div>
          <div className="h-64">
            {statusData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie data={statusData} dataKey="value" nameKey="name" innerRadius={55} outerRadius={85} paddingAngle={4} isAnimationActive={true}>
                    {statusData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={STATUS_COLORS[entry.name] || '#9ca3af'} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">No status data yet</div>
            )}
          </div>
        </div>
      </div>



        {/* Recent Bookings */}
        <div className="bg-white p-5 md:p-6 shadow-md hover:shadow-lg transition-shadow duration-300 rounded-lg">
          <div className="flex justify-between items-center mb-5">
            <h3 className="text-lg font-bold text-gray-800">Recent Bookings</h3>
            <CustomButton
              label="View All"
              color="primary"
              variant="light"
              size="sm"
              endContent={<FaEye />}
              onPress={handleViewAllBookings}
            />
          </div>
          <div className="space-y-3">
            {bookingItems.slice(0, 4).map((booking, index) => (
              <div
                key={index}
                className="flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gray-50 hover:bg-gray-100 transition-colors duration-200 p-4 rounded-lg border border-gray-100 cursor-pointer"
                onClick={() => handleOpenBookingDetails(booking)}
                title="Click to view booking details"
              >
                <div className="flex items-center space-x-3 md:space-x-4 flex-1">
                  <div className="w-14 h-14 sm:w-16 sm:h-16 rounded-lg overflow-hidden bg-gray-200 flex items-center justify-center">
                    {Array.isArray(booking.serviceImages) && booking.serviceImages.length > 0 ? (
                      <img
                        src={booking.serviceImages[0]?.startsWith('http')
                          ? booking.serviceImages[0]
                          : getImageUrlWithFallback(booking.serviceImages[0] as string, 'service-images')}
                        alt={booking.serviceName || booking.service}
                        className="w-full h-full object-cover"
                        onError={(e) => { (e.target as HTMLImageElement).src = 'https://via.placeholder.com/64x64?text=Service'; }}
                      />
                    ) : (
                      <FaUser className="text-blue-600 text-xl" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base md:text-lg font-medium">{booking.serviceName || booking.service}</h4>
                    <p className="text-sm text-gray-500">
                      {booking.date || booking.appointmentDate}
                    </p>
                    <p className="text-sm text-gray-600 font-medium">{booking.provider}</p>
                    <p className="text-xs text-gray-500">{booking.email}</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3 w-full sm:w-auto mt-3 sm:mt-0">
                  <StatusChip status={booking.status} />

                </div>
              </div>
            ))}
          </div>
          {bookingsLoading && (
            <div className="text-center py-6 text-gray-500">Loading bookings...</div>
          )}
          {!bookingsLoading && bookingItems.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>No recent bookings found</p>
            </div>
          )}
        </div>
      </div>
  );
}
